{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/project/SSS_SLAM/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/project/SSS_SLAM/src/frontend/include/**", "/home/<USER>/project/SSS_SLAM/src/input_data/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}