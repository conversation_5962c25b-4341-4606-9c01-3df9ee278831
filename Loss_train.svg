<svg viewBox="0 0 1796.77783203125 400" xmlns="http://www.w3.org/2000/svg"><g><g><g><g><g><line x1="51.5625" y1="377" x2="46.5625" y2="377" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="329.875" x2="46.5625" y2="329.875" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="282.75" x2="46.5625" y2="282.75" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="235.625" x2="46.5625" y2="235.625" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="188.5" x2="46.5625" y2="188.5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="141.375" x2="46.5625" y2="141.375" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="94.25000000000004" x2="46.5625" y2="94.25000000000004" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="47.12500000000004" x2="46.5625" y2="47.12500000000004" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="51.5625" y1="0" x2="46.5625" y2="0" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line></g><g transform="translate(41.5625, 0)"><text x="0" y="377" dx="0em" dy="0.3em" style="text-anchor: end; visibility: hidden; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0</text><text x="0" y="329.875" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.05</text><text x="0" y="282.75" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.1</text><text x="0" y="235.625" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.15</text><text x="0" y="188.5" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.2</text><text x="0" y="141.375" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.25</text><text x="0" y="94.25000000000004" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.3</text><text x="0" y="47.12500000000004" dx="0em" dy="0.3em" style="text-anchor: end; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.35</text><text x="0" y="0" dx="0em" dy="0.3em" style="text-anchor: end; visibility: hidden; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0.4</text></g><line x1="51.5625" y1="0" x2="51.5625" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line></g></g><g transform="translate(51, 0)" clip-path="url(#clip_0)"><clipPath id="clip_0"><rect width="1745" height="377"></rect></clipPath><g><g><g><line x1="0" y1="0" x2="0" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="145.43479166666665" y1="0" x2="145.43479166666665" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="290.8695833333333" y1="0" x2="290.8695833333333" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="436.304375" y1="0" x2="436.304375" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="581.7391666666666" y1="0" x2="581.7391666666666" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="727.1739583333333" y1="0" x2="727.1739583333333" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="872.60875" y1="0" x2="872.60875" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1018.0435416666667" y1="0" x2="1018.0435416666667" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1163.4783333333332" y1="0" x2="1163.4783333333332" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1308.913125" y1="0" x2="1308.913125" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1454.3479166666666" y1="0" x2="1454.3479166666666" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1599.7827083333332" y1="0" x2="1599.7827083333332" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="1745.2175" y1="0" x2="1745.2175" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line></g><g><line x1="0" y1="377" x2="1745.2175" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="329.875" x2="1745.2175" y2="329.875" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="282.75" x2="1745.2175" y2="282.75" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="235.625" x2="1745.2175" y2="235.625" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="188.5" x2="1745.2175" y2="188.5" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="141.375" x2="1745.2175" y2="141.375" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="94.25000000000004" x2="1745.2175" y2="94.25000000000004" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="47.12500000000004" x2="1745.2175" y2="47.12500000000004" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line><line x1="0" y1="0" x2="1745.2175" y2="0" fill="rgb(0, 0, 0)" stroke="rgb(66, 66, 66)" stroke-width="1px" opacity="0.25"></line></g></g></g><g><g><line x1="0" y1="377" x2="1745.2175" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(153, 153, 153)" stroke-width="1.5px"></line></g></g><g><g><line x1="145.43479166666665" y1="0" x2="145.43479166666665" y2="377" fill="rgb(0, 0, 0)" stroke="rgb(153, 153, 153)" stroke-width="1.5px"></line></g></g><g><g><g><g><g><path stroke="rgb(255, 112, 67)" stroke-width="2px" d="M145.43479166666665,0.2628202438354492L159.9782708333333,136.6689648926258L174.52175,207.54535008966923L189.06522916666668,240.05592887103558L203.60870833333334,255.3719465509057L218.1521875,263.01584082469344L232.69566666666665,271.924112316221L247.2391458333333,272.96953820437193L261.782625,273.7928527854383L276.32610416666665,274.22343834117055L290.8695833333333,274.85633970052004L305.41306249999997,275.5151809640229L319.9565416666666,275.6702445708215L334.50002083333334,275.9673667214811L349.0435,276.0845316648483L363.58697916666665,276.2932165786624L378.13045833333337,276.62353955954313L392.6739375,276.8580169118941L407.2174166666667,277.0020697526634L421.76089583333334,277.07110472768545L436.304375,277.1276542805135L450.8478541666667,277.47386141493917L465.3913333333333,277.6687688268721L479.9348125,277.4600628465414L494.4782916666666,277.53538266569376L509.02177083333333,277.4665092006326L523.56525,277.5142248608172L538.1087291666666,277.749390386045L552.6522083333333,277.5348489806056L567.1956875,277.7463357411325L581.7391666666666,277.86227882653475L596.2826458333334,278.08542239293456L610.8261249999999,277.9256679750979L625.3696041666667,278.07521917670965L639.9130833333332,277.926166549325L654.4565625,277.9249376691878L669.0000416666667,277.88908948004246L683.5435208333333,278.02795293554664L698.087,278.07877941802144L712.6304791666666,277.99072840064764L727.1739583333333,278.0750366002321L741.7174375,278.2319400161505L756.2609166666667,278.04981295764446L770.8043958333333,278.30698597058654L785.347875,277.93060456216335L799.8913541666666,278.2034370191395L814.4348333333334,278.2279865331948L828.9783124999999,278.0792147926986L843.5217916666667,278.19581796228886L858.0652708333333,278.2557943351567L872.60875,278.40486100688577L887.1522291666666,278.36784713715315L901.6957083333334,278.2128256633878L916.2391875000001,278.2958347611129L930.7826666666666,278.43181912600994L945.3261458333333,278.4195865020156L959.869625,278.1723287962377L974.4131041666667,278.34048875421286L988.9565833333332,278.44964842125773L1003.5000624999999,278.37933541089296L1018.0435416666667,278.2195809930563L1032.5870208333333,278.43743686378L1047.1305,278.3306928239763L1061.6739791666666,278.4402808435261L1076.2174583333333,278.2741502933204L1090.7609375,278.1906075105071L1105.3044166666666,278.50316439568996L1119.8478958333335,278.2918110564351L1134.391375,278.4221566170454L1148.9348541666666,278.5268080495298L1163.4783333333332,278.6358764283359L1178.0218125000001,278.334217954427L1192.5652916666668,278.38629438355565L1207.1087708333332,278.5613009594381L1221.6522499999999,278.418245267123L1236.1957291666668,278.5816090814769L1250.7392083333334,278.3708736933768L1265.2826874999998,278.4913601242006L1279.8261666666665,278.6334888897836L1294.3696458333334,278.8714351952076L1308.913125,278.54305735602975L1323.4566041666667,278.59316055476665L1338.0000833333334,278.4961071126163L1352.5435625,278.56394129619L1367.0870416666667,278.39308482408524L1381.6305208333333,278.52951860800385L1396.174,278.4700969867408L1410.7174791666666,278.3386278785765L1425.2609583333333,278.59939624369144L1439.8044375,278.561694201082L1454.3479166666666,278.59639777615666L1468.8913958333333,278.63540594279766L1483.434875,278.65679547935724L1497.9783541666666,278.58373679965734L1512.5218333333335,278.52143608778715L1527.0653125,278.5195611678064L1541.6087916666665,278.59054128453135L1556.1522708333334,278.5107764303684L1570.69575,278.8694268539548L1585.2392291666665,278.85280537232757" style="fill: none;" fill="none"></path></g><g><path stroke="rgb(0, 119, 187)" stroke-width="2px" d="M145.43479166666665,-8.568038821220398L159.9782708333333,44.35829785466194L174.52175,56.69310849905014L189.06522916666668,12.856833308935165L203.60870833333334,-13.234496966004372L218.1521875,-21.270951732993126L232.69566666666665,116.76613454520702L247.2391458333333,180.99406753480434L261.782625,205.5640863329172L276.32610416666665,202.86129438132048L290.8695833333333,132.77983312308788L305.41306249999997,45.618609234690666L319.9565416666666,28.54037743806839L334.50002083333334,201.65051740407944L349.0435,271.895012434572L363.58697916666665,190.5108383372426L378.13045833333337,45.95412862300873L392.6739375,179.87873592227697L407.2174166666667,148.37918140739202L421.76089583333334,189.78195090591908L436.304375,242.79068575054407L450.8478541666667,191.8182825371623L465.3913333333333,211.80029489845037L479.9348125,225.85521471500397L494.4782916666666,301.11178516224027L509.02177083333333,232.59454787522554L523.56525,315.33214735612273L538.1087291666666,316.8201386258006L552.6522083333333,337.4485700279474L567.1956875,316.743786547333L581.7391666666666,265.8643779605627L596.2826458333334,240.12904372811317L610.8261249999999,134.8941249102354L625.3696041666667,189.86859046667814L639.9130833333332,212.69674540311098L654.4565625,189.93464101850986L669.0000416666667,111.51124645769596L683.5435208333333,40.81802760064602L698.087,103.69219814240932L712.6304791666666,285.3966061472893L727.1739583333333,299.9438996128738L741.7174375,180.56349602341652L756.2609166666667,150.37717197835445L770.8043958333333,193.34497299790382L785.347875,195.14940441399813L799.8913541666666,202.40174938738346L814.4348333333334,242.7594932615757L828.9783124999999,184.91489738970995L843.5217916666667,199.17968184500933L858.0652708333333,270.67509961128235L872.60875,266.16833970695734L887.1522291666666,267.9970327280462L901.6957083333334,321.49955618940294L916.2391875000001,329.9310046322644L930.7826666666666,335.5663961432874L945.3261458333333,310.97708743810654L959.869625,276.7137604281306L974.4131041666667,213.71232003718615L988.9565833333332,155.85745774954557L1003.5000624999999,206.6796005219221L1018.0435416666667,202.2479638159275L1032.5870208333333,177.21713603287935L1047.1305,93.24772816896439L1061.6739791666666,42.76465800404549L1076.2174583333333,166.51444674283266L1090.7609375,293.9697514921427L1105.3044166666666,303.9917254522443L1119.8478958333335,176.18661821633577L1134.391375,182.7237409055233L1148.9348541666666,185.42861142009497L1163.4783333333332,195.23237840086222L1178.0218125000001,219.30171632021666L1192.5652916666668,238.36917501688004L1207.1087708333332,183.47967774420977L1221.6522499999999,198.89587373286486L1236.1957291666668,288.2860193476081L1250.7392083333334,247.58271437883377L1265.2826874999998,302.30535781756043L1279.8261666666665,326.90248571150005L1294.3696458333334,335.7748563475907L1308.913125,336.0790287591517L1323.4566041666667,297.7464793510735L1338.0000833333334,281.7277528978884L1352.5435625,158.38153541833162L1367.0870416666667,179.68561214208603L1381.6305208333333,210.13362445682287L1396.174,223.2261836603284L1410.7174791666666,138.9246270507574L1425.2609583333333,85.72414477169514L1439.8044375,21.722550436854362L1454.3479166666666,251.6234829351306L1468.8913958333333,309.5074942596257L1483.434875,272.80364640802145L1497.9783541666666,170.42166762799025L1512.5218333333335,198.53389479964972L1527.0653125,193.77672423422337L1541.6087916666665,183.26456052064896L1556.1522708333334,248.24022845178843L1570.69575,206.46702532470226L1585.2392291666665,193.17969915270805" style="fill: none;" fill="none"></path></g></g></g></g></g></g><g transform="translate(51, 377)" clip-path="url(#clip_1)"><clipPath id="clip_1"><rect width="1745" height="23"></rect></clipPath><g><g><line x1="0" y1="0" x2="0" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="145.43479166666665" y1="0" x2="145.43479166666665" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="290.8695833333333" y1="0" x2="290.8695833333333" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="436.304375" y1="0" x2="436.304375" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="581.7391666666666" y1="0" x2="581.7391666666666" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="727.1739583333333" y1="0" x2="727.1739583333333" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="872.60875" y1="0" x2="872.60875" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1018.0435416666667" y1="0" x2="1018.0435416666667" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1163.4783333333332" y1="0" x2="1163.4783333333332" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1308.913125" y1="0" x2="1308.913125" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1454.3479166666666" y1="0" x2="1454.3479166666666" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1599.7827083333332" y1="0" x2="1599.7827083333332" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line><line x1="1745.2175" y1="0" x2="1745.2175" y2="5" style="visibility: inherit;" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line></g><g transform="translate(0, 8)"><text x="0" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: hidden; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">-10</text><text x="145.43479166666665" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">0</text><text x="290.8695833333333" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">10</text><text x="436.304375" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">20</text><text x="581.7391666666666" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">30</text><text x="727.1739583333333" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">40</text><text x="872.60875" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">50</text><text x="1018.0435416666667" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">60</text><text x="1163.4783333333332" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">70</text><text x="1308.913125" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">80</text><text x="1454.3479166666666" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">90</text><text x="1599.7827083333332" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: inherit; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">100</text><text x="1745.2175" y="0" dx="0em" dy="0.95em" style="text-anchor: middle; visibility: hidden; font-family: Roboto, sans-serif; font-size: 12px; font-weight: 200;" fill="rgb(33, 33, 33)" stroke="none" stroke-width="1px">110</text></g><line x1="0" y1="0" x2="1745.2175" y2="0" fill="rgb(0, 0, 0)" stroke="rgb(204, 204, 204)" stroke-width="1px"></line></g></g></g></g></svg>