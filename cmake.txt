cmake .. -D<PERSON><PERSON>VDB_BUILD_CORE=ON -DOPENVDB_BUILD_NANOVDB=ON -DUSE_NANOVDB=ON -DNANOVDB_BUILD_UNITTESTS=ON -DNANOVDB_BUILD_EXAMPLES=ON -DNANOVDB_BUILD_BENCHMARK=ON -DNANOVDB_USE_INTRINSICS=ON -DNANOVDB_USE_CUDA=ON -DNANOVDB_CUDA_KEEP_PTX=ON -DTBB_ROOT=/usr/local/include/tbb -DBOOST_ROOT=$CONDA_PREFIX/include/boost -DBLOSC_ROOT=$CONDA_PREFIX/lib/cmake/Blosc -DGTEST_ROOT=$CONDA_PREFIX/lib/cmake/GTest -DCMAKE_INSTALL_PREFIX=/usr/local

cmake .. -DNANOVDB_USE_OPENVDB=ON -DUSE_NANOVDB=ON -DTBB_ROOT=/usr/local/include/tbb -DBOOST_ROOT=$CONDA_PREFIX/include/boost -DBLOSC_ROOT=$CONDA_PREFIX -DCMAKE_INSTALL_PREFIX=/usr/local

-DTBB_ROOT=/usr/local/include/tbb -DBOOST_ROOT=$CONDA_PREFIX/include/boost -DBLOSC_ROOT=/usr/include -DGTEST_ROOT=$CONDA_PREFIX/lib/cmake/GTest -DCMAKE_INSTALL_PREFIX=/usr/local