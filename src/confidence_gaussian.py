import torch
import math
import torch.nn as nn
import torch.nn.functional as F

class HeightConv2d(nn.Module):
  def __init__(self, kernel_size=3, sigma = 1.5):
    super(HeightConv2d, self).__init__()
    self.kernel_size = kernel_size
    self.padding = kernel_size // 2
    self.sigma = sigma
    self.gaussian_kernel = self._create_gaussian_kernel(kernel_size, sigma)

  def _create_gaussian_kernel(self, kernel_size, sigma):
    # 生成高斯核
    center = kernel_size // 2
    kernel = torch.zeros(kernel_size, kernel_size)
    for i in range(kernel_size):
        for j in range(kernel_size):
            x, y = i - center, j - center
            kernel[i, j] = math.exp(-(x**2 + y**2) / (2 * sigma**2))
    kernel /= kernel.sum()  # 归一化
    return kernel

  def forward(self, height, confidence):
    # 根据置信度生成卷积核的权重
    height = height.reshape(1, 1, height.shape[0], height.shape[1])
    confidence = confidence.reshape(1, 1, confidence.shape[0], confidence.shape[1])

    # 填充输入，以确保输出尺寸与输入一致
    height = F.pad(height, pad=(self.padding, self.padding, self.padding, self.padding), mode='replicate')
    confidence = F.pad(confidence, pad=(self.padding, self.padding, self.padding, self.padding), mode='replicate')

    # 提取形状信息
    batch_size, _, height_size, width_size = confidence.shape

    # 生成卷积核权重
    kernel_weights = confidence.unfold(2, self.kernel_size, 1).unfold(3, self.kernel_size, 1)
    kernel_weights = kernel_weights.contiguous().view(batch_size, -1, self.kernel_size, self.kernel_size)

    # 将置信度生成的权重与高斯核相乘
    gaussian_kernel = self.gaussian_kernel.to(confidence.device)
    kernel_weights = kernel_weights * gaussian_kernel

    # 对卷积核进行归一化
    kernel_weights = kernel_weights / kernel_weights.sum(dim=(2, 3), keepdim=True)

    # 对高度值通道进行动态卷积
    height_windows = height.unfold(2, self.kernel_size, 1).unfold(3, self.kernel_size, 1)
    height_windows = height_windows.contiguous().view(batch_size, -1, self.kernel_size, self.kernel_size)

    output = (kernel_weights * height_windows).sum(dim=(2, 3), keepdim=True)

    output = output.view(batch_size, height_size  - 2 * self.padding, width_size - 2 * self.padding)
    return output

def gaussian_kernel(kernel_size=3, sigma=1.5):
    # 生成高斯核
    kernel = torch.zeros(kernel_size, kernel_size)
    center = kernel_size // 2
    for i in range(kernel_size):
        for j in range(kernel_size):
            x, y = i - center, j - center
            kernel[i, j] = math.exp(-(x**2 + y**2) / (2 * sigma**2))
    kernel /= kernel.sum()  # 归一化
    return kernel

def apply_gaussian_conv(confidence, kernel, kernel_size):
    # 扩展卷积核维度
    kernel = kernel.unsqueeze(0).unsqueeze(0).to(confidence.device)  # [1, 1, kernel_size, kernel_size]
    
    # 对置信度进行卷积
    conf_conv = F.conv2d(confidence.unsqueeze(0).unsqueeze(0), kernel, padding=kernel_size//2).squeeze(0).squeeze(0)
    
    return conf_conv

def confidence_gaussian(image, kernel_size, sigma, iter):
  kernel = gaussian_kernel(kernel_size, sigma)
  height_conv = HeightConv2d(kernel_size, sigma)
  for i in range(iter):
    image[0] = height_conv(image[0], image[1])
    image[1] = apply_gaussian_conv(image[1], kernel, kernel_size)
  return image