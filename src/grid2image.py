import torch
import matplotlib.pyplot as plt
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import kornia

def show_images(image):
  image_np = image.to("cpu").numpy()
  plt.figure(figsize=(20, 5))  # 设置画布大小

  # 第一张图像
  plt.subplot(1, 2, 1)  # 1行2列，第1个位置
  plt.imshow(image_np[0], cmap='gray')
  plt.colorbar()
  plt.title("Elevation Map")

  # 第二张图像
  plt.subplot(1, 2, 2)  # 1行2列，第2个位置
  plt.imshow(image_np[1], cmap='gray')
  plt.colorbar()
  plt.title("Confidence Map")

  plt.show()

def grid_to_height(grid, bbox_min, bbox_max, mod = "max", show = False):
  bbox_size = bbox_max - bbox_min

  grid_mask = grid.enabled_mask[0].jdata
  ijk_data = grid.ijk.jdata[grid_mask] # 取出体素坐标
  #------------------------------------------------
  height = bbox_size[1] + 1# 定义图像高度
  width = bbox_size[0] + 1# 定义图像宽度
  image = torch.zeros(2, height, width, dtype=torch.float32).to("cuda") # 创建图像张量


  x_coords = ijk_data[:, 0] - bbox_min[0] # 提取 x 坐标，并把最小值设为0
  y_coords = ijk_data[:, 1] - bbox_min[1] # 提取 y 坐标，并把最小值设为0

  z_values = ((ijk_data[:, 2] - bbox_min[2]).float() / bbox_size[2]).to("cuda") # 提取 z 值，并归一化

  # 将 x, y 坐标转换为索引
  indices = (y_coords * width + x_coords).to("cuda").long()
  
  # 使用 scatter_ 将 z 值按 (x, y) 坐标放入 image 中，取最大值
  if (mod == "max"): # 优化结果
    image[1].fill_(0.01) # 没有被优化的像素置信度取0.1
    image[0].view(-1).scatter_reduce_(
          dim=0,  # 沿一维索引操作
          index=indices,  # 目标索引
          src=z_values,  # 要填充的值
          reduce="amax",  # 对重复位置取最大值
          include_self=False  # 不包括 tensor 的初始值
      )
    image[1].view(-1)[indices] = 0.99 # 重建的像素置信度为0.99
  elif (mod == "mean"):
    # 填充第二个通道为常量 0.3
    image[1].fill_(0.01) # 没有被深度初始化的位置置信度为0.1
    image[0].view(-1).scatter_reduce_(
          dim=0,  # 沿一维索引操作
          index=indices,  # 目标索引
          src=z_values,  # 要填充的值
          reduce="mean",  # 对重复位置取最大值
          include_self=False  # 不包括 tensor 的初始值
      )
    image[1].view(-1)[indices] = 0.3 # 设置第二个通道的值为 0.3
    height_image = image[0].reshape(1, 1, height, width)
    median_image = kornia.filters.median_blur(height_image, kernel_size=(11, 11))
    gaussian_image = kornia.filters.gaussian_blur2d(median_image, (15, 15), (9, 9))
    image[0] = gaussian_image.reshape(height, width)
    mask = image[0] > 0.001  # 不是最深的地方都设为0.1
    image[1][mask] = 0.1

  if (show):
    show_images(image)

  return image

def merge_height(height_image0, height_image1, show = False):
  merge_image = torch.zeros_like(height_image0)
  # 比较置信度并选择更大的值
  mask = height_image0[1] > height_image1[1]  # 比较置信度
  merge_image[0] = torch.where(mask, height_image0[0], height_image1[0])  # 选择像素值
  merge_image[1] = torch.where(mask, height_image0[1], height_image1[1])  # 选择置信度

  if (show):
    show_images(merge_image)
  
  return merge_image