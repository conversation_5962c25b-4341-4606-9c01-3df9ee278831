from tqdm import tqdm
import numpy as np
import torch
def print_pointcloud(pointcloud, pc_path, mask = None):
  with open(pc_path, "w") as f:
    for i in tqdm(range(pointcloud.shape[0]), desc="Processing", unit="point"):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = pointcloud[i][0]
      y = pointcloud[i][1]
      z = pointcloud[i][2]
      r = 255
      g = 0
      b = 0
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def print_ray_orig(ray_orig, ro_path, mask = None):
  with open(ro_path, "w") as f:
    for i in range(ray_orig.shape[0]):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = ray_orig[i][0]
      y = ray_orig[i][1]
      z = ray_orig[i][2]
      r = 0
      g = 255
      b = 0
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def print_dir(ray_orig, ray_dir, rd_path, mask = None):
  with open(rd_path, "w") as f:
    for i in range(ray_dir.shape[0]):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = ray_orig[i][0] + ray_dir[i][0]
      y = ray_orig[i][1] + ray_dir[i][1]
      z = ray_orig[i][2] + ray_dir[i][2]
      r = 0
      g = 0
      b = 255
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def print_image_pc(image, pc_path, voxel_sizes, z_scale):
  H, W = image.shape
  with open(pc_path, "w") as f:
    for x in tqdm(range(W), desc="Processing", unit="column"):
      for y in range(H):
        pc_z = image[y][x] / z_scale
        pc_x = float(x) * voxel_sizes[0][0]
        pc_y = float(y) * voxel_sizes[0][1]
        r = 0
        g = 255
        b = 0
        f.write(f"{pc_x} {pc_y} {pc_z} {r} {g} {b}" + "\n")

def image_to_pc(image, voxel_sizes, z_scale):
  H, W = image.shape
  pointcloud = []
  for x in tqdm(range(W), desc="Processing", unit="column"):
    for y in range(H):
      pc_z = image[y][x] / z_scale
      pc_x = float(x) * voxel_sizes[0][0]
      pc_y = float(y) * voxel_sizes[0][1]
      pointcloud.append([pc_x, pc_y, pc_z])
  pointcloud = torch.tensor(pointcloud, dtype=torch.float32)
  return pointcloud