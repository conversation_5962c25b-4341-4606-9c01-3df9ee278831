import numpy as np
import torch

from tqdm import tqdm
import numpy as np
import torch

class scene_info:
  def __init__(self, pointcloud):
    self.bb_min = np.nanmin(pointcloud, axis=1) # 最小点
    self.bb_max = np.nanmax(pointcloud, axis=1) # 最大点
    self.center = (self.bb_min + self.bb_max) / 2 # 中心点
    total_size = (self.bb_max - self.bb_min).max() # 场景边长
    self.scale = total_size / 0.8 # 场景尺度

def print_pointcloud(pointcloud, pc_path, mask = None):
  with open(pc_path, "w") as f:
    for i in tqdm(range(pointcloud.shape[0]), desc="Processing", unit="point"):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = pointcloud[i][0]
      y = pointcloud[i][1]
      z = pointcloud[i][2]
      r = 255
      g = 0
      b = 0
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def print_ray_orig(ray_orig, ro_path, mask = None):
  with open(ro_path, "w") as f:
    for i in range(ray_orig.shape[0]):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = ray_orig[i][0]
      y = ray_orig[i][1]
      z = ray_orig[i][2]
      r = 0
      g = 255
      b = 0
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def print_dir(ray_orig, ray_dir, rd_path, mask = None):
  with open(rd_path, "w") as f:
    for i in range(ray_dir.shape[0]):
      if (mask is not None) and (mask[i] == 0):
        continue
      x = ray_orig[i][0] + ray_dir[i][0]
      y = ray_orig[i][1] + ray_dir[i][1]
      z = ray_orig[i][2] + ray_dir[i][2]
      r = 0
      g = 0
      b = 255
      f.write(f"{x} {y} {z} {r} {g} {b}" + "\n")

def image_to_pc(image, voxel_sizes, z_scale):
  H, W = image.shape
  pointcloud = []
  for x in tqdm(range(W), desc="Processing", unit="column"):
    for y in range(H):
      pc_z = image[y][x] / z_scale
      pc_x = float(x) * voxel_sizes[0][0]
      pc_y = float(y) * voxel_sizes[0][1]
      pointcloud.append([pc_x, pc_y, pc_z])
  pointcloud = torch.tensor(pointcloud, dtype=torch.float32)
  return pointcloud

def createPC(keypings, print_origPC = False, print_dir = None):
  pointcloud = []
  for kp in keypings:
    pointcloud.append(kp.points)
  pointcloud = np.concatenate(pointcloud, axis=1)

  si = scene_info(pointcloud)

  pointcloud = (pointcloud - si.center[:, np.newaxis]) / si.scale

  pointcloud = torch.tensor(pointcloud, dtype=torch.float32).T
  rows_with_nan = torch.any(torch.isnan(pointcloud), dim=1) # 使用布尔索引过滤掉包含 NaN 的行
  pointcloud = pointcloud[~rows_with_nan]

  if print_origPC:
    orig_pc = []
    for kp in keypings:
      orig_pc.append(kp.points[:, kp.mask.astype(bool)])
    orig_pc = np.concatenate(orig_pc, axis = 1)
    orig_pc =  (orig_pc - si.center[:, np.newaxis]) / si.scale
    orig_pc = torch.tensor(orig_pc, dtype=torch.float32).T
    rows_with_nan = torch.any(torch.isnan(orig_pc), dim=1) # 使用布尔索引过滤掉包含 NaN 的行
    orig_pc = orig_pc[~rows_with_nan]
    #打印初始化水底点云
    print_pointcloud(orig_pc, print_dir)

  return pointcloud, si