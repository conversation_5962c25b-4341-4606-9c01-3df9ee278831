import struct
import numpy as np
import src.params as params
from scipy.spatial.transform import Rotation as R

tbl = params.tbl
Rbl = params.Rbl
tbr = params.tbr
Rbr = params.Rbr
ping_res = params.ping_res
range_max = params.range_max

class KeyPing:
  def __init__(self, p, q, height, intensity, elevation):
    self.p = np.array(p, dtype=np.float32)
    q_new = [q[1], q[2], q[3], q[0]]
    self.R = R.from_quat(q_new).as_matrix()
    self.height = height
    self.intensity = np.array(intensity, dtype=np.float32)
    self.elevation = np.array(elevation, dtype=np.float32)

    # 设置阈值
    threshold = params.intensity_th / 255.0
    # 创建掩码
    self.mask = (self.intensity > threshold).astype(np.float32)

    range_left = np.linspace(range_max, 0, ping_res)
    range_right = np.linspace(0, range_max, ping_res)

    self.range = np.concatenate((range_left, range_right))

    self.theta_cov = 1.0/180*np.pi

    self.points = self.get_points() # 水底点云
    self.high_points = self.get_high_points() # 测量弧线高点

    self.left_sonar_orig = (np.dot(self.R, tbl)+ self.p).reshape(3, 1) # 左声纳传感器位置
    self.right_sonar_orig = (np.dot(self.R, tbr) + self.p).reshape(3, 1) # 右声纳传感器位置

  def get_points(self):
    pts_y = np.sin(self.elevation) * self.range
    pts_x = np.zeros_like(self.range)
    pts_z =  np.cos(self.elevation) * self.range
    # 得到声呐坐标系下的点云
    pts = np.array([pts_x, pts_y, pts_z])
    pts_left = pts[:, :ping_res]
    pts_right = pts[:, ping_res:]
    b_pts_l = np.dot(Rbl.T, pts_left) + tbl[:, np.newaxis]
    b_pts_r = np.dot(Rbr.T, pts_right) + tbr[:, np.newaxis]
    b_pts = np.concatenate((b_pts_l, b_pts_r), axis=1)

    # 得到世界坐标系下的点云
    w_pts = np.dot(self.R, b_pts) + self.p[:, np.newaxis]
    return w_pts
  
  def get_high_points(self):
    left_elevation = self.elevation[:ping_res] - self.theta_cov
    right_elevation = self.elevation[ping_res:] + self.theta_cov
    left_range = self.range[:ping_res]
    right_range = self.range[ping_res:]
    
    pts_y_left = np.sin(left_elevation) * left_range
    pts_x_left = np.zeros_like(left_range)
    pts_z_left =  np.cos(left_elevation) * left_range

    pts_y_right = np.sin(right_elevation) * right_range
    pts_x_right = np.zeros_like(right_range)
    pts_z_right =  np.cos(right_elevation) * right_range

    # 得到声呐坐标系下的点云

    pts_left = np.array([pts_x_left, pts_y_left, pts_z_left])
    pts_right = np.array([pts_x_right, pts_y_right, pts_z_right])

    b_pts_l = np.dot(Rbl.T, pts_left) + tbl[:, np.newaxis]
    b_pts_r = np.dot(Rbr.T, pts_right) + tbr[:, np.newaxis]
    b_pts = np.concatenate((b_pts_l, b_pts_r), axis=1)

    # 得到世界坐标系下的点云
    w_pts = np.dot(self.R, b_pts) + self.p[:, np.newaxis]
    return w_pts

  def get_rays(self):
    left_sonar_1000 = np.tile(self.left_sonar_orig.reshape(3, 1), (1, 1000))
    right_sonar_1000 = np.tile(self.right_sonar_orig, (1, 1000))
    sonar_orig = np.concatenate((left_sonar_1000, right_sonar_1000), axis=1)
    sonar_dir = self.points - sonar_orig
    sonar_dir = sonar_dir / np.linalg.norm(sonar_dir, axis=0)
    Px = sonar_orig + sonar_dir * (self.range * np.cos(self.theta_cov))
    Px2P0 = self.points - Px
    ray_orig = self.high_points + Px2P0
    ray_dir = self.points - ray_orig
    ray_dir = ray_dir / np.linalg.norm(ray_dir, axis=0)
    return ray_orig.T, ray_dir.T

def load_keypings(path, parse_rario):
  ping_res = params.ping_res
  format_str = f"3f4f1f{2*ping_res}f{2*ping_res}f" # 每个结构体的格式
  struct_size = struct.calcsize(format_str)  # 计算每个结构体的大小
  # 打开二进制文件
  with open(path, "rb") as file:
    # 读取文件内容
    data = file.read()

  # 解析每个结构体
  num_structures = len(data) // struct_size  # 计算文件中有多少个结构体
  key_pings = []  # 用于存储解析后的结构体数据

  for i in range(int(num_structures * parse_rario)):
    # 提取当前结构体的数据
    start = i * struct_size
    end = start + struct_size
    struct_data = data[start:end]

    # 解析结构体数据
    unpacked_data = struct.unpack(format_str, struct_data)

    # 解析后的数据是一个元组，可以根据需要进行处理
    p = [x for x in unpacked_data[:3]]  # 位置
    q = unpacked_data[3:7]  # 四元数
    height = unpacked_data[7]  # 水深
    intensity = unpacked_data[8:8+2*ping_res]  # 强度
    elevation = unpacked_data[8+2*ping_res:]  # 初始仰角

    # 创建 KeyPing 对象并添加到列表
    key_ping = KeyPing(p, q, height, intensity, elevation)
    key_pings.append(key_ping)

  return key_pings